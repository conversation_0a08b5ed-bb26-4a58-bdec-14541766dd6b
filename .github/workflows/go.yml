# Go Continuous Integration Workflow

name: Go CI

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the "main" branch
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  # This workflow contains a single job called "build-and-test"
  build-and-test:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v4

      # Set up Go environment
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23' # You can change this to your desired Go version

      # Cache Go modules to speed up future builds
      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      # Install dependencies
      - name: Install dependencies
        working-directory: ./
        run: go mod download

      # Run linting
      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v6
        with:
          # Optional: version of golangci-lint to use in form of v1.2 or v1.2.3 or `latest` to use the latest version
          version: latest
          working-directory: ./
          # Optional: show only new issues if it's a pull request. The default value is `false`.
          only-new-issues: true


      # Run tests
      - name: Test
        working-directory: ./
        run: go test -v ./...

      # Build the application
      - name: Build
        working-directory: ./
        run: go build -v ./...